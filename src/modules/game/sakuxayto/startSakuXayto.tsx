import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Keyboard,
  Alert,
} from 'react-native';
import DraggableFlatList, {
  ScaleDecorator,
  RenderItemParams,
} from 'react-native-draggable-flatlist';
import CountBadge from '../components/CountQuestions';
import HeadGame from '../components/HeadGame';
import LineProgressBar from '../components/LineProgressBar';
import Lives from '../components/Lives';
import {useGameHook} from '../../../redux/hook/gameHook';
import {useSelector} from 'react-redux';
import {RootState} from '../../../redux/store/store';
import {useDhbcHook} from '../../../redux/hook/game/dhbcHook';
import {SafeAreaView} from 'react-native-safe-area-context';

import {BottomGame} from '../components/BottomGame';
import ModelConfirm from '../components/ModelConfirm';
import HintModel from '../components/HintModel';
import GameOverModal from '../components/GameOverModel';
import ModelDoneLevel from '../components/ModelDoneLevel';

const SakuXayTo = () => {
  const dhbcHook = useDhbcHook();
  const gameHook = useGameHook();
  const {totalLives, currentLives, isGameOver, messageGameOver, gem, cup} =
    useSelector((state: RootState) => state.gameStore);
  const {totalQuestion, questionDone, currentQuestion, isWinLevel} =
    useSelector((state: RootState) => state.dhbcStore);

  const [isShowKeyboard, setIsShowKeyboard] = useState<boolean>(false);
  const [isError, setIsError] = useState<boolean>(false);
  const [isCorrect, setIsCorrect] = useState<boolean>(false);
  const [showModelConfirm, setShowModelConfirm] = useState<boolean>(false);
  const [showHintModel, setShowHintModel] = useState<boolean>(false);

  // Drag and drop game state
  type WordItem = {
    id: string;
    word: string;
    isPlaced: boolean;
  };

  const correctAnswer = ['Hello', 'I', 'am', 'a', 'mobile', 'developer'];
  const [availableWords, setAvailableWords] = useState<WordItem[]>([]);
  const [dropZones, setDropZones] = useState<(WordItem | null)[]>([]);

  // Initialize game data
  useEffect(() => {
    initializeGame();
  }, []);

  const initializeGame = () => {
    // Create shuffled words for dragging
    const shuffledWords = [...correctAnswer]
      .sort(() => Math.random() - 0.5)
      .map((word, index) => ({
        id: `word-${index}`,
        word,
        isPlaced: false,
      }));

    setAvailableWords(shuffledWords);
    setDropZones(new Array(correctAnswer.length).fill(null));
  };

  const hiddenInputRef = useRef<TextInput | null>(null);

  useEffect(() => {
    startGame();
    Keyboard.addListener('keyboardDidShow', () => {
      setIsShowKeyboard(true);
    });
    Keyboard.addListener('keyboardDidHide', () => {
      setIsShowKeyboard(false);
      hiddenInputRef.current?.blur();
    });
    return () => {
      if (hiddenInputRef.current) {
        hiddenInputRef.current.focus();
      }
    };
  }, []);

  useEffect(() => {
    if (currentLives === 0) {
      gameOver('Thất bại rồi, làm lại nào');
    }
  }, [currentLives]);
  useEffect(() => {
    if (isWinLevel) {
      winGame();
    }
  }, [isWinLevel]);

  const startGame = () => {
    resetQuestion();
    gameHook.restartGame();
    dhbcHook.startGame();
  };

  // Thua
  const gameOver = (message: string) => {
    gameHook.gameOver(message);
  };

  // Thắng
  const winGame = () => {
    gameHook.setData({stateName: 'gem', value: gem + 30});
    gameHook.setData({stateName: 'cup', value: cup + 10});
  };

  // Reset câu hỏi
  const resetQuestion = () => {
    setIsCorrect(false);
    setIsError(false);
  };

  // Handle word placement in drop zone
  const handleWordDrop = (wordItem: WordItem, zoneIndex: number) => {
    const newDropZones = [...dropZones];
    const newAvailableWords = [...availableWords];

    // Remove word from previous position if it was already placed
    const previousIndex = newDropZones.findIndex(
      item => item?.id === wordItem.id,
    );
    if (previousIndex !== -1) {
      newDropZones[previousIndex] = null;
    }

    // If zone is occupied, move that word back to available
    if (newDropZones[zoneIndex]) {
      const displacedWord = newDropZones[zoneIndex]!;
      displacedWord.isPlaced = false;
      newAvailableWords.push(displacedWord);
    }

    // Place new word in zone
    wordItem.isPlaced = true;
    newDropZones[zoneIndex] = wordItem;

    // Remove word from available words
    const wordIndex = newAvailableWords.findIndex(w => w.id === wordItem.id);
    if (wordIndex !== -1) {
      newAvailableWords.splice(wordIndex, 1);
    }

    setDropZones(newDropZones);
    setAvailableWords(newAvailableWords);
  };

  // Remove word from drop zone back to available
  const removeWordFromZone = (zoneIndex: number) => {
    const newDropZones = [...dropZones];
    const newAvailableWords = [...availableWords];

    const wordToRemove = newDropZones[zoneIndex];
    if (wordToRemove) {
      wordToRemove.isPlaced = false;
      newAvailableWords.push(wordToRemove);
      newDropZones[zoneIndex] = null;

      setDropZones(newDropZones);
      setAvailableWords(newAvailableWords);
    }
  };

  // Kiểm tra đáp án
  const checkAnswer = () => {
    setIsError(false);
    setIsCorrect(false);

    // Check if all zones are filled
    if (dropZones.some(zone => zone === null)) {
      Alert.alert('Incomplete', 'Please fill all positions before checking!');
      return;
    }

    // Check if the order is correct
    const userAnswer = dropZones.map(zone => zone?.word);
    const isCorrectOrder = userAnswer.every(
      (word, index) => word === correctAnswer[index],
    );

    if (isCorrectOrder) {
      setIsCorrect(true);
      Alert.alert('Correct!', 'Well done! Moving to next question...');
      setTimeout(() => {
        resetQuestion();
        initializeGame(); // Reset the game
        dhbcHook.setData({stateName: 'questionDone', value: questionDone + 1});
        dhbcHook.nextQuestion();
      }, 2000);
    } else {
      setIsError(true);
      Alert.alert('Incorrect', 'Try again! The order is not correct.');
      gameHook.setData({stateName: 'currentLives', value: currentLives - 1});
    }
  };

  // Sử dụng gợi ý
  const useHint = () => {
    gameHook.setData({stateName: 'gem', value: gem - 10});
    setShowModelConfirm(false);
    setShowHintModel(true);
  };
  return (
    <SafeAreaView style={{flex: 1}}>
      <View style={styles.container}>
        {/* Header */}
        <HeadGame
          isShowSuggest={true}
          onUseHint={() => setShowModelConfirm(true)}
          timeOut={() => gameOver('Hết giờ rồi, làm lại nào')}
        />
        {!isShowKeyboard ? (
          <View>
            <LineProgressBar
              progress={(questionDone / totalQuestion) * 100}></LineProgressBar>
            <View
              style={{
                width: '100%',
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
              <Lives
                totalLives={totalLives}
                currentLives={currentLives}></Lives>
              <CountBadge
                current={questionDone}
                total={totalQuestion}></CountBadge>
            </View>
          </View>
        ) : null}
        {/* Game Content */}
        <View style={styles.gameContent}>
          {/* Question Header */}
          <Text style={styles.questionHeader}>
            Arrange external and inner words available for the correct answer
          </Text>

          {/* Drop Zones for Answer */}
          <View style={styles.dropZonesContainer}>
            <Text style={styles.sectionTitle}>Answer Area:</Text>
            <View style={styles.dropZonesRow}>
              {dropZones.map((zone, index) => (
                <TouchableOpacity
                  key={`zone-${index}`}
                  style={[
                    styles.dropZone,
                    zone ? styles.dropZoneFilled : styles.dropZoneEmpty,
                  ]}
                  onPress={() => zone && removeWordFromZone(index)}>
                  <Text style={styles.dropZoneText}>
                    {zone ? zone.word : `${index + 1}`}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Available Words for Dragging */}
          <View style={styles.availableWordsContainer}>
            <Text style={styles.sectionTitle}>Available Words:</Text>
            <DraggableFlatList
              data={availableWords}
              onDragEnd={({data}) => setAvailableWords(data)}
              keyExtractor={item => item.id}
              renderItem={({
                item,
                drag,
                isActive,
              }: RenderItemParams<WordItem>) => (
                <ScaleDecorator>
                  <TouchableOpacity
                    onLongPress={drag}
                    disabled={isActive}
                    style={[
                      styles.wordItem,
                      isActive && styles.wordItemActive,
                    ]}>
                    <Text style={styles.wordText}>{item.word}</Text>
                  </TouchableOpacity>
                </ScaleDecorator>
              )}
              horizontal={false}
              numColumns={2}
              contentContainerStyle={styles.wordsGrid}
            />
          </View>

          {/* Quick Drop Buttons */}
          <View style={styles.quickDropContainer}>
            <Text style={styles.sectionTitle}>Quick Place:</Text>
            <View style={styles.quickDropRow}>
              {availableWords.slice(0, 4).map(word => (
                <View key={word.id} style={styles.quickDropGroup}>
                  <TouchableOpacity style={styles.quickWordButton}>
                    <Text style={styles.quickWordText}>{word.word}</Text>
                  </TouchableOpacity>
                  <View style={styles.quickDropButtons}>
                    {dropZones.map((zone, zoneIndex) => (
                      <TouchableOpacity
                        key={`quick-${word.id}-${zoneIndex}`}
                        style={[
                          styles.quickDropButton,
                          zone && styles.quickDropButtonDisabled,
                        ]}
                        disabled={zone !== null}
                        onPress={() => handleWordDrop(word, zoneIndex)}>
                        <Text style={styles.quickDropButtonText}>
                          {zoneIndex + 1}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
              ))}
            </View>
          </View>

          {/* Check Answer Button */}
          <TouchableOpacity style={styles.checkButton} onPress={checkAnswer}>
            <Text style={styles.checkButtonText}>Check Answer</Text>
          </TouchableOpacity>
        </View>

        {/* Bottom */}
        <View>
          <BottomGame resetGame={startGame} />
        </View>
      </View>
      <View style={{zIndex: 1000}}>
        <ModelConfirm
          isShow={showModelConfirm}
          closeModal={() => setShowModelConfirm(false)}
          onConfirm={useHint}
        />
        <HintModel
          isShow={showHintModel}
          closeModal={() => setShowHintModel(false)}
          text={currentQuestion.hint}
        />
        <GameOverModal
          visible={isGameOver}
          onClose={() => {}}
          restartGame={startGame}
          message={messageGameOver}
          isTimeOut={false}
        />
        <ModelDoneLevel
          visible={isWinLevel}
          onNextLevel={startGame}
          currentGem={gem - 30}
          currentCup={cup - 10}
          gemAdd={30}
          cupAdd={10}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    margin: 16,
  },
  gameContent: {
    marginTop: 32,
    flex: 1,
    alignItems: 'center',
  },

  imageContainer: {
    width: '90%',
    height: 200,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 10,
  },

  answerContainer: {
    position: 'relative',
    marginTop: 32,
    maxWidth: '70%',
    minWidth: 200,
    backgroundColor: 'white',
    borderRadius: 15,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 30,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,

    elevation: 5,
  },
  answerContainerError: {
    borderColor: '#FF6B6B',
    borderWidth: 3,
    borderRadius: 12,
  },
  answerContainerCorrect: {
    borderColor: '#2EB553',
    borderWidth: 3,
    borderRadius: 12,
  },
  answerText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  errorText: {
    fontSize: 12,
    textAlign: 'center',

    backgroundColor: '#FCF8E8',
    paddingHorizontal: 10,
    paddingVertical: 3,
    borderRadius: 12,
    color: '#FF6B6B',
    fontWeight: 'bold',
    marginTop: 5,
  },
  correctText: {
    fontSize: 12,
    textAlign: 'center',

    backgroundColor: '#E8F8FC',
    paddingHorizontal: 10,
    paddingVertical: 3,
    borderRadius: 12,
    color: '#2EB553',
    fontWeight: 'bold',
    marginTop: 5,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  skipButton: {
    backgroundColor: '#FF6B6B',
    paddingHorizontal: 25,
    paddingVertical: 15,
    borderRadius: 15,
    flex: 1,
    marginRight: 10,
  },
  skipButtonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: 16,
  },
  checkButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 25,
    paddingVertical: 15,
    borderRadius: 15,
    flex: 1,
    marginLeft: 10,
  },
  checkButtonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: 16,
  },
  hiddenInput: {
    width: 0, // Chiều rộng 0
    height: 0, // Chiều cao 0
    opacity: 0, // Hoàn toàn trong suốt
    position: 'absolute', // Không chiếm không gian trong layout
    // Để đảm bảo nó thực sự không nhìn thấy và không tương tác ngoài ý muốn:
    top: -10000, // Đẩy ra rất xa màn hình
    left: -10000,
  },
  // Drag and Drop Game Styles
  questionHeader: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
    color: '#555',
  },
  dropZonesContainer: {
    width: '100%',
    marginBottom: 20,
    paddingHorizontal: 16,
  },
  dropZonesRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 8,
  },
  dropZone: {
    minWidth: 80,
    height: 40,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 4,
    borderWidth: 2,
  },
  dropZoneEmpty: {
    backgroundColor: '#f0f0f0',
    borderColor: '#ddd',
    borderStyle: 'dashed',
  },
  dropZoneFilled: {
    backgroundColor: '#e8f5e8',
    borderColor: '#4CAF50',
    borderStyle: 'solid',
  },
  dropZoneText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  availableWordsContainer: {
    width: '100%',
    marginBottom: 20,
    paddingHorizontal: 16,
  },
  wordsGrid: {
    paddingVertical: 10,
  },
  wordItem: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 12,
    margin: 4,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    minWidth: 80,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
  },
  wordItemActive: {
    backgroundColor: '#e3f2fd',
    borderColor: '#2196F3',
    transform: [{scale: 1.05}],
  },
  wordText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  quickDropContainer: {
    width: '100%',
    marginBottom: 20,
    paddingHorizontal: 16,
  },
  quickDropRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
  },
  quickDropGroup: {
    alignItems: 'center',
    marginBottom: 10,
  },
  quickWordButton: {
    backgroundColor: '#f5f5f5',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    marginBottom: 5,
  },
  quickWordText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#333',
  },
  quickDropButtons: {
    flexDirection: 'row',
    gap: 4,
  },
  quickDropButton: {
    backgroundColor: '#2196F3',
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quickDropButtonDisabled: {
    backgroundColor: '#ccc',
  },
  quickDropButtonText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#fff',
  },
});

export default SakuXayTo;
