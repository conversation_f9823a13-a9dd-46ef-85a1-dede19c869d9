import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Keyboard,
  Image,
} from 'react-native';
import CountBadge from '../components/CountQuestions';
import HeadGame from '../components/HeadGame';
import LineProgressBar from '../components/LineProgressBar';
import Lives from '../components/Lives';
import {useGameHook} from '../../../redux/hook/gameHook';
import {useSelector} from 'react-redux';
import {RootState} from '../../../redux/store/store';
import {useDhbcHook} from '../../../redux/hook/game/dhbcHook';
import {SafeAreaView} from 'react-native-safe-area-context';
import {CardTitleGame} from '../components/CardTitleGame';
import {BottomGame} from '../components/BottomGame';
import ModelConfirm from '../components/ModelConfirm';
import HintModel from '../components/HintModel';
import GameOverModal from '../components/GameOverModel';
import ModelDoneLevel from '../components/ModelDoneLevel';

const SakuXayTo = () => {
  const dhbcHook = useDhbcHook();
  const gameHook = useGameHook();
  const {totalLives, currentLives, isGameOver, messageGameOver, gem, cup} =
    useSelector((state: RootState) => state.gameStore);
  const {totalQuestion, questionDone, currentQuestion, isWinLevel} =
    useSelector((state: RootState) => state.dhbcStore);

  const [isShowKeyboard, setIsShowKeyboard] = useState<boolean>(false);
  const [isError, setIsError] = useState<boolean>(false);
  const [isCorrect, setIsCorrect] = useState<boolean>(false);
  const [answer, setAnswer] = useState<string>('');
  const [showModelConfirm, setShowModelConfirm] = useState<boolean>(false);
  const [showHintModel, setShowHintModel] = useState<boolean>(false);

  const hiddenInputRef = useRef<TextInput | null>(null);

  useEffect(() => {
    startGame();
    Keyboard.addListener('keyboardDidShow', () => {
      setIsShowKeyboard(true);
    });
    Keyboard.addListener('keyboardDidHide', () => {
      setIsShowKeyboard(false);
      hiddenInputRef.current?.blur();
    });
    return () => {
      if (hiddenInputRef.current) {
        hiddenInputRef.current.focus();
      }
    };
  }, []);

  useEffect(() => {
    if (currentLives === 0) {
      gameOver('Thất bại rồi, làm lại nào');
    }
  }, [currentLives]);
  useEffect(() => {
    if (isWinLevel) {
      winGame();
    }
  }, [isWinLevel]);

  const showKeyboard = () => {
    if (hiddenInputRef.current) {
      hiddenInputRef.current.focus();
    }
  };
  const startGame = () => {
    resetQuestion();
    gameHook.restartGame();
    dhbcHook.startGame();
  };

  // Thua
  const gameOver = (message: string) => {
    gameHook.gameOver(message);
  };

  // Thắng
  const winGame = () => {
    gameHook.setData({stateName: 'gem', value: gem + 30});
    gameHook.setData({stateName: 'cup', value: cup + 10});
  };

  // Reset câu hỏi
  const resetQuestion = () => {
    setIsCorrect(false);
    setIsError(false);
    setAnswer('');
  };

  // Kiểm tra đáp án
  const checkAnswer = () => {
    setIsError(false);
    setIsCorrect(false);
    if (answer.toLowerCase() === currentQuestion.answer) {
      setIsCorrect(true);
      setTimeout(() => {
        resetQuestion();
        dhbcHook.setData({stateName: 'questionDone', value: questionDone + 1});
        dhbcHook.nextQuestion();
      }, 2000);
    } else {
      setIsError(true);
      gameHook.setData({stateName: 'currentLives', value: currentLives - 1});
    }
  };

  // Sử dụng gợi ý
  const useHint = () => {
    gameHook.setData({stateName: 'gem', value: gem - 10});
    setShowModelConfirm(false);
    setShowHintModel(true);
  };
  return (
    <SafeAreaView style={{flex: 1}}>
      <View style={styles.container}>
        {/* Header */}
        <HeadGame
          isShowSuggest={true}
          onUseHint={() => setShowModelConfirm(true)}
          timeOut={() => gameOver('Hết giờ rồi, làm lại nào')}
        />
        {!isShowKeyboard ? (
          <View>
            <LineProgressBar
              progress={(questionDone / totalQuestion) * 100}></LineProgressBar>
            <View
              style={{
                width: '100%',
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
              <Lives
                totalLives={totalLives}
                currentLives={currentLives}></Lives>
              <CountBadge
                current={questionDone}
                total={totalQuestion}></CountBadge>
            </View>
          </View>
        ) : null}
        {/* Game Content */}
        <View style={styles.gameContent}></View>

        {/* Bottom */}
        <View>
          <BottomGame resetGame={startGame} />
        </View>
      </View>
      <View style={{zIndex: 1000}}>
        <ModelConfirm
          isShow={showModelConfirm}
          closeModal={() => setShowModelConfirm(false)}
          onConfirm={useHint}
        />
        <HintModel
          isShow={showHintModel}
          closeModal={() => setShowHintModel(false)}
          text={currentQuestion.hint}
        />
        <GameOverModal
          visible={isGameOver}
          onClose={() => {}}
          restartGame={startGame}
          message={messageGameOver}
          isTimeOut={false}
        />
        <ModelDoneLevel
          visible={isWinLevel}
          onNextLevel={startGame}
          currentGem={gem - 30}
          currentCup={cup - 10}
          gemAdd={30}
          cupAdd={10}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    margin: 16,
  },
  gameContent: {
    marginTop: 32,
    flex: 1,
    alignItems: 'center',
  },

  imageContainer: {
    width: '90%',
    height: 200,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 10,
  },

  answerContainer: {
    position: 'relative',
    marginTop: 32,
    maxWidth: '70%',
    minWidth: 200,
    backgroundColor: 'white',
    borderRadius: 15,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 30,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,

    elevation: 5,
  },
  answerContainerError: {
    borderColor: '#FF6B6B',
    borderWidth: 3,
    borderRadius: 12,
  },
  answerContainerCorrect: {
    borderColor: '#2EB553',
    borderWidth: 3,
    borderRadius: 12,
  },
  answerText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  errorText: {
    fontSize: 12,
    textAlign: 'center',

    backgroundColor: '#FCF8E8',
    paddingHorizontal: 10,
    paddingVertical: 3,
    borderRadius: 12,
    color: '#FF6B6B',
    fontWeight: 'bold',
    marginTop: 5,
  },
  correctText: {
    fontSize: 12,
    textAlign: 'center',

    backgroundColor: '#E8F8FC',
    paddingHorizontal: 10,
    paddingVertical: 3,
    borderRadius: 12,
    color: '#2EB553',
    fontWeight: 'bold',
    marginTop: 5,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  skipButton: {
    backgroundColor: '#FF6B6B',
    paddingHorizontal: 25,
    paddingVertical: 15,
    borderRadius: 15,
    flex: 1,
    marginRight: 10,
  },
  skipButtonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: 16,
  },
  checkButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 25,
    paddingVertical: 15,
    borderRadius: 15,
    flex: 1,
    marginLeft: 10,
  },
  checkButtonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: 16,
  },
  hiddenInput: {
    width: 0, // Chiều rộng 0
    height: 0, // Chiều cao 0
    opacity: 0, // Hoàn toàn trong suốt
    position: 'absolute', // Không chiếm không gian trong layout
    // Để đảm bảo nó thực sự không nhìn thấy và không tương tác ngoài ý muốn:
    top: -10000, // Đẩy ra rất xa màn hình
    left: -10000,
  },
});

export default SakuXayTo;
